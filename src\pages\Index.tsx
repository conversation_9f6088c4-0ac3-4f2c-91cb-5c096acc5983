import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, Sparkles, Globe, Users, Loader2, AlertCircle, Database } from 'lucide-react';
import Navbar from '@/components/Navbar';
import TopicCard from '@/components/TopicCard';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { getTodayRangeInUTC } from '@/lib/timezone-utils';
import { useTimezone } from '@/hooks/useTimezone';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import { withCache, CacheKeys, CacheTTL } from '@/lib/cache-utils';
import heroBg from '@/assets/diverse-social-hero-bg.jpg';

// Stats data will be translated in component

// 主题颜色映射
const topicGradients = [
  'from-purple-500 to-blue-600',
  'from-orange-500 to-red-600', 
  'from-green-500 to-emerald-600',
  'from-blue-500 to-cyan-600',
  'from-pink-500 to-rose-600',
  'from-indigo-500 to-purple-600'
];

const Index = () => {
  const [topics, setTopics] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { timezone, isLoaded: timezoneLoaded } = useTimezone();
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Stats data with translations
  const stats = [
    { label: t('home.stats.topics'), value: '6+', icon: TrendingUp },
    { label: t('home.stats.content'), value: '500+', icon: Globe },
    { label: t('home.stats.platforms'), value: '6+', icon: Sparkles },
    { label: t('home.stats.users'), value: '1.2k+', icon: Users }
  ];

  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setLoading(true);
        setError(null);

        // 获取今天的时间范围（使用用户选择的时区）
        const { start: todayStart, end: todayEnd } = getTodayRangeInUTC(timezone);

        // Platform to summary_type mapping (consistent with content summary page)
        const getSummaryTypeForPlatform = (platform: string) => {
          switch (platform) {
            case 'blog': return 'blog_post';
            case 'youtube': return 'youtube_video';
            case 'podcast': return 'podcast';
            case 'xiaohongshu': return 'xiaohongshu_post';
            case 'reddit': return 'daily_subreddit';
            case 'twitter-rss': return 'twitter_rss_datasource';
            case 'wechat': return 'wechat_post';
            default: return null;
          }
        };

        // 构建数据源查询
        let datasourcesQuery = supabase
          .from('datasources')
          .select('id, topic_id, platform, source_name, language')
          .eq('is_active', true);

        // 英文页面只显示EN数据源，中文页面显示所有数据源
        if (language === 'en') {
          datasourcesQuery = datasourcesQuery.eq('language', 'EN');
        }

        // 构建摘要查询，需要根据语言过滤
        let summariesQuery = supabase
          .from('summaries')
          .select('summary_type, metadata, created_at, language')
          .gte('created_at', todayStart.toISOString())
          .lte('created_at', todayEnd.toISOString());

        // 应用语言过滤逻辑 - 简化查询避免URL过长
        if (language === 'en') {
          // 英文页面：只显示EN语言的摘要
          // 移除复杂的数据源过滤，改为在客户端过滤
          summariesQuery = summariesQuery.eq('language', 'EN');
        } else {
          // 中文页面：只显示ZH摘要
          summariesQuery = summariesQuery.eq('language', 'ZH');
        }

        // 优化查询：一次性获取所有需要的数据，使用缓存
        const [topicsResult, datasourcesResult, summariesResult] = await Promise.all([
          // 获取主题数据（使用缓存）
          withCache(
            CacheKeys.TOPICS,
            () => supabase
              .from('topics')
              .select('*')
              .eq('is_active', true)
              .order('name'),
            CacheTTL.TOPICS
          ),

          // 获取所有活跃的数据源（使用缓存）
          withCache(
            CacheKeys.DATASOURCES(language),
            () => datasourcesQuery,
            CacheTTL.DATASOURCES
          ),

          // 获取今天的摘要数据（不缓存，因为需要实时性）
          summariesQuery
        ]);

        if (topicsResult.error) throw topicsResult.error;
        if (datasourcesResult.error) throw datasourcesResult.error;
        if (summariesResult.error) throw summariesResult.error;

        const topics = topicsResult.data || [];
        const datasources = datasourcesResult.data || [];
        const summaries = summariesResult.data || [];

        // 构建数据源映射
        const datasourcesByTopic = new Map<string, Array<{platform: string, source_name: string}>>();
        datasources.forEach(ds => {
          if (!datasourcesByTopic.has(ds.topic_id)) {
            datasourcesByTopic.set(ds.topic_id, []);
          }
          datasourcesByTopic.get(ds.topic_id)!.push({
            platform: ds.platform,
            source_name: ds.source_name
          });
        });

        // 构建摘要计数映射 - 按主题名称统计
        const summaryCountByTopic = new Map<string, number>();

        // 客户端过滤：确保摘要来自对应语言的数据源
        const filteredSummaries = summaries.filter(summary => {
          const datasourceId = summary.metadata?.datasource_id;
          const sourceName = summary.metadata?.source_name;

          // 检查是否来自对应语言的数据源
          const matchingDatasource = datasources.find(ds => {
            const idMatch = datasourceId && ds.id === datasourceId;
            const nameMatch = sourceName && ds.source_name === sourceName;
            return idMatch || nameMatch;
          });

          if (!matchingDatasource) return false;

          // 英文页面额外检查：确保数据源是EN语言
          if (language === 'en' && matchingDatasource.language !== 'EN') {
            return false;
          }

          return true;
        });

        filteredSummaries.forEach(summary => {
          const topicName = summary.metadata?.topic_name;
          if (topicName) {
            summaryCountByTopic.set(topicName, (summaryCountByTopic.get(topicName) || 0) + 1);
          }
        });

        // 为每个主题计算统计数据
        const topicsWithStats = topics.map(topic => {
          const topicDatasources = datasourcesByTopic.get(topic.id) || [];
          const platforms = new Set<string>();

          // 从数据源获取平台信息
          topicDatasources.forEach(ds => {
            platforms.add(ds.platform);
          });

          // 直接从摘要统计中获取数量，使用主题名称匹配
          const totalSummaryCount = summaryCountByTopic.get(topic.name) || 0;

          return {
            ...topic,
            summaryCount: totalSummaryCount,
            platforms: Array.from(platforms),
          };
        });

        setTopics(topicsWithStats);
      } catch (err) {
        console.error('Error fetching topics:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch topics');
      } finally {
        setLoading(false);
      }
    };

    // 只有在时区加载完成后才获取数据
    if (timezoneLoaded) {
      fetchTopics();
    }
  }, [timezoneLoaded, timezone, language]);

  // 平台名称映射
  const platformNames: { [key: string]: string } = {
    blog: 'Blog',
    reddit: 'Reddit',
    youtube: 'YouTube',
    twitter: 'Twitter',
    'twitter-rss': 'Twitter RSS',
    podcast: 'Podcast',
    xiaohongshu: 'Rednote',
    wechat: 'Wechat',
    linkedin: 'LinkedIn'
  };

  // 转换数据格式以适配TopicCard组件
  const formattedTopics = topics.map((topic, index) => ({
    id: topic.id,
    name: topic.name,
    description: topic.description || '暂无描述',
    contentCount: topic.summaryCount || 0,
    platforms: topic.platforms?.map((p: string) => platformNames[p] || p) || [],
    isPopular: (topic.summaryCount || 0) > 10, // 超过10条summary的主题标记为热门
    gradient: topicGradients[index % topicGradients.length]
  }));

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      {/* Hero Section - Enhanced with better spacing and visual hierarchy */}
      <section className="relative overflow-hidden min-h-[80vh] flex items-center">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat transform scale-105"
          style={{ backgroundImage: `url(${heroBg})` }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black/50 via-black/40 to-black/30" />

        <div className="relative container-wide mx-auto responsive-padding py-24 text-center text-white space-4xl">
          <div className="max-w-4xl mx-auto space-lg">
            <h1 className="text-display-1 text-responsive-3xl leading-tight blur-fade-in">
              {t('home.heroTitle')}
            </h1>
            <p className="text-body-large text-responsive-xl text-gray-200 max-w-3xl mx-auto leading-relaxed slide-in-bottom">
              {t('home.heroSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row responsive-gap justify-center bounce-in">
              <Button
                size="lg"
                className="btn-primary-interactive btn-xl text-body-large modern-button shadow-button hover:shadow-glow-intense ripple-effect"
                onClick={() => {
                  document.getElementById('topics-section')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                {t('home.getStarted')}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section - Enhanced with better visual hierarchy */}
      <section className="responsive-padding py-16 lg:py-24 bg-gradient-to-br from-muted/20 via-background to-muted/30">
        <div className="container-wide mx-auto responsive-padding">
          <div className="grid grid-cols-2 md:grid-cols-4 responsive-gap">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center border-0 bg-background/60 backdrop-blur card-3d group hover:bg-gradient-card-hover">
                <CardContent className="pt-8 pb-6 space-md">
                  <div className="w-16 h-16 mx-auto bg-gradient-primary rounded-2xl flex items-center justify-center mb-4 shadow-card group-hover:shadow-glow group-hover:scale-110 transition-all duration-300 transform-gpu">
                    <stat.icon className="h-8 w-8 text-primary-foreground group-hover:animate-pulse" />
                  </div>
                  <div className="text-3xl lg:text-4xl font-bold text-gradient group-hover:scale-105 transition-transform duration-300">{stat.value}</div>
                  <div className="text-sm lg:text-base text-muted-foreground group-hover:text-foreground transition-colors duration-300">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Topics Section - Enhanced with better spacing and animations */}
      <section id="topics-section" className="py-16 lg:py-24">
        <div className="container-wide mx-auto responsive-padding space-4xl">
          <div className="text-center mb-16 space-lg py-8">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-gradient leading-normal">{t('home.topics.title')}</h2>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {t('home.topics.subtitle')}
            </p>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-muted-foreground">{t('home.loading')}</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="flex justify-center items-center py-12">
              <AlertCircle className="h-8 w-8 text-destructive mr-2" />
              <span className="text-destructive">{t('home.error')}: {error}</span>
            </div>
          )}

          {/* Topics Grid - Enhanced responsive layout */}
          {!loading && !error && (
            <>
              {formattedTopics.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-16">
                  {formattedTopics.map((topic, index) => (
                    <div
                      key={topic.id}
                      className="bounce-in"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <TopicCard {...topic} />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-16 space-lg">
                  <div className="w-24 h-24 mx-auto bg-muted/50 rounded-full flex items-center justify-center mb-6">
                    <Sparkles className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <p className="text-lg text-muted-foreground">{t('home.topics.noTopics')}</p>
                </div>
              )}
            </>
          )}


        </div>
      </section>

      {/* CTA Section - Enhanced with gradient background and better spacing */}
      <section className="py-20 lg:py-32 bg-gradient-hero text-primary-foreground relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative container-wide mx-auto responsive-padding text-center space-3xl">
          <div className="max-w-4xl mx-auto space-lg">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 blur-fade-in">
              {t('home.cta.title')}
            </h2>
            <p className="text-lg md:text-xl mb-10 opacity-90 max-w-3xl mx-auto leading-relaxed slide-in-bottom">
              {t('home.cta.subtitle')}
            </p>
            <div className="bounce-in">
              <Button
                size="lg"
                variant="secondary"
                className="btn-secondary-interactive text-lg px-10 py-4 shadow-button hover:shadow-glow-intense transition-all duration-300 modern-button ripple-effect"
                asChild
              >
                <Link to={language === 'zh' ? '/zh/content-summary' : '/content-summary'}>{t('home.getStarted')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
